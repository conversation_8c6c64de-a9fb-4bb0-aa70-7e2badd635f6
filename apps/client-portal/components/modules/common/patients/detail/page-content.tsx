"use client";

import { Card } from "flowbite-react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { lazy, useState } from "react";

// Lazy load heavy components
const PatientOverview = lazy(() =>
  import("./components/patient-overview").then((mod) => ({
    default: mod.PatientOverview,
  })),
);
const TableDataConsents = lazy(() =>
  import("./components/patient-tabs/consents").then((mod) => ({
    default: mod.TableDataConsents,
  })),
);
const PatientVisitsTab = lazy(() =>
  import("./components/patient-tabs/patient-visits").then((mod) => ({
    default: mod.PatientVisitsTab,
  })),
);
const AddVisit = lazy(() =>
  import("./components/patient-tabs/patient-visits/add-visit").then((mod) => ({
    default: mod.AddVisit,
  })),
);
const CustomVisit = lazy(() =>
  import("./components/patient-tabs/patient-visits/custom-visit").then(
    (mod) => ({ default: mod.CustomVisit }),
  ),
);

import { PatientDetailBreadcrumb } from "@/components/breadcrumb/patient-detail-breadcrumb";
import { ComingSoon } from "@/components/shared/coming-soon";
import { EntitlementGate } from "@/components/shared/entitlement/entitlement-gate";
import { NoPermission } from "@/components/shared/permission/no-permission";
import { PermissionGate } from "@/components/shared/permission/permission-gate";
import { useRole } from "@/hooks/auth";
import { useUserTypeStore } from "@/stores/user-type";

import { TabButton } from "./components/tab-button";
import { usePatient } from "./hooks/use-patient";
import { usePatientTabs } from "./hooks/use-patient-tabs";

type AddVisitModal = "custom" | "visit";

export const PatientDetailPageContent = () => {
  const [addVisitModal, setAddVisitModal] = useState<AddVisitModal | null>(
    null,
  );

  const { is } = useRole();
  const isCro = is("cro");

  const router = useRouter();

  const { patientId } = useParams();
  const { tab } = usePatientTabs();
  const { data: patient, isLoading } = usePatient({
    patientId: patientId as string,
  });

  function renderTabContent() {
    if (!patient) return null;

    switch (tab) {
      case "visits":
        return (
          <PatientVisitsTab
            patient={patient}
            setAddVisitModal={setAddVisitModal}
          />
        );

      case "consents":
        return <TableDataConsents />;

      default:
        return <ComingSoon />;
    }
  }

  if (addVisitModal === "custom" && patient?.currentProtocolId) {
    return (
      <div className="flex h-fit flex-col gap-2.5">
        <PatientDetailBreadcrumb patient={patient} />
        <CustomVisit
          onGoBack={() => setAddVisitModal(null)}
          protocolId={patient?.currentProtocolId}
        />
      </div>
    );
  }

  if (addVisitModal === "visit") {
    return (
      <div className="flex h-full max-h-[calc(100vh-var(--navbar-height)-var(--footer-height)-20px)] flex-col gap-2.5 overflow-hidden">
        <PatientDetailBreadcrumb patient={patient} />
        <AddVisit
          onGoBack={() => setAddVisitModal(null)}
          protocolId={patient?.currentProtocolId}
        />
      </div>
    );
  }

  return (
    <div className="flex h-full flex-col gap-2.5">
      <PatientDetailBreadcrumb patient={patient} />
      <PermissionGate
        action="read:own"
        subject="patients"
        fallback={<NoPermission />}
      >
        <PatientOverview patient={patient} isLoading={isLoading} />
        <Card className="flex-1 [&>div]:justify-start [&>div]:p-0">
          <div className="flex flex-wrap items-center gap-2.5 border-b">
            <TabButton label="Visits" value="visits" />
            <TabButton label="Consents" value="consents" />
            <TabButton
              label="Source Medical Records"
              value="documents"
              onClick={() => {
                if (isCro) {
                  router.push(
                    `/cro/studies/${patient?.studyId}/patients/${patientId}/ctmr`,
                  );
                } else {
                  router.push(
                    `/sit/studies/${patient?.studyId}/patients/${patientId}/ctmr`,
                  );
                }
              }}
            />
            <TabButton label="Adverse Events" value="adverse-events" />
            <TabButton label="Medical History" value="medical-history" />
            <TabButton label="Concomitant Meds" value="concomitant-meds" />
            <EntitlementGate entitlement="EDC_CORE">
              <TabButton
                label="Electronic Data Capture"
                value="edc"
                onClick={() => {
                  if (isCro) {
                    router.push(
                      `/cro/studies/${patient?.studyId}/patients/${patientId}/edc`,
                    );
                  } else {
                    router.push(
                      `/sit/studies/${patient?.studyId}/patients/${patientId}/edc`,
                    );
                  }
                }}
              />
            </EntitlementGate>
          </div>

          {renderTabContent()}
        </Card>
      </PermissionGate>
    </div>
  );
};
