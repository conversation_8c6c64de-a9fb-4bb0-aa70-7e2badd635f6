"use client";

import { Card } from "flowbite-react";
import { LayoutGrid } from "lucide-react";
import { parseAsStringEnum, useQueryState } from "nuqs";

import { PatientEdcBreadcrumb } from "@/components/breadcrumb";
import { EntitlementGate } from "@/components/shared/entitlement/entitlement-gate";
import { Button } from "@/components/ui/button";

import { PatientDashboard } from "./components/patient-dashboard";
import { PatientForm } from "./components/patient-forms";

enum View {
  Dashboard = "dashboard",
  Forms = "forms",
}

export default function EdcDashboard() {
  const [view, setView] = useQueryState(
    "view",
    parseAsStringEnum<View>(Object.values(View)).withDefault(View.Dashboard),
  );

  return (
    <EntitlementGate
      entitlement="EDC_CORE"
      fallback={
        <div className="flex h-full w-full items-center justify-center">
          <Card className="rounded-xl p-8">
            <div className="text-center">
              <h2 className="mb-4 text-2xl font-bold text-gray-900">
                Access Restricted
              </h2>
              <p className="text-gray-600">
                You don't have access to Electronic Data Capture (EDC)
                functionality. Please contact your administrator to request
                access.
              </p>
            </div>
          </Card>
        </div>
      }
    >
      <div className="flex h-full flex-col gap-5">
        <PatientEdcBreadcrumb />

        <Card className="rounded-xl">
          <div className="flex items-center justify-between p-5">
            <div className="flex flex-col gap-2">
              <h1 className="font-plus-jakarta text-4xl font-bold">
                {view === View.Dashboard
                  ? "Patient Dashboard"
                  : "Patient Forms"}
              </h1>
              <span className="text-base font-normal italic leading-normal text-gray-500">
                {view === View.Dashboard
                  ? "Monitor and review patient forms across their study journey"
                  : "Monitor and review clinical forms across all assigned sites"}
              </span>
            </div>

            <div className="flex gap-2.5">
              <Button
                variant={view === View.Dashboard ? "default" : "outline"}
                onClick={() => setView(View.Dashboard)}
              >
                Patient Dashboard
              </Button>
              <Button
                variant={view === View.Forms ? "default" : "outline"}
                onClick={() => setView(View.Forms)}
              >
                <LayoutGrid size={24} />
                <span>Schedule of Activities</span>
              </Button>
            </div>
          </div>
        </Card>

        {view === View.Dashboard ? <PatientDashboard /> : <PatientForm />}
      </div>
    </EntitlementGate>
  );
}
