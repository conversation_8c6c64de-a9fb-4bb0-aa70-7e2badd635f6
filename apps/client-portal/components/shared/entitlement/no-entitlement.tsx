import { ComponentProps } from "react";

import { cn } from "@/lib/utils";

export const NoEntitlement = (props: ComponentProps<"div">) => {
  return (
    <div
      {...props}
      className={cn(
        "text-primary-500 flex flex-1 flex-col items-center justify-center gap-[30px]",
        props.className,
      )}
    >
      <svg
        width="54"
        height="55"
        viewBox="0 0 54 55"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <rect
          x="1.5"
          y="2"
          width="51"
          height="51"
          rx="25.5"
          stroke="#7200FF"
          strokeWidth="3"
        />
        <path
          d="M13.8846 12H40.1154C40.7478 12 41.3544 12.2512 41.8016 12.6984C42.2488 13.1456 42.5 13.7522 42.5 14.3846V38.2308C42.5 39.4956 41.9975 40.7087 41.1031 41.6031C40.2087 42.4975 38.9956 43 37.7308 43H16.2692C15.0044 43 13.7913 42.4975 12.8969 41.6031C12.0025 40.7087 11.5 39.4956 11.5 38.2308V14.3846C11.5 13.7522 11.7512 13.1456 12.1984 12.6984C12.6456 12.2512 13.2522 12 13.8846 12V12Z"
          stroke="#7200FF"
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M11.5 27.5H42.5"
          stroke="#7200FF"
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M26.997 20.346C27.6555 20.346 28.1893 19.8122 28.1893 19.1537C28.1893 18.4952 27.6555 17.9614 26.997 17.9614C26.3385 17.9614 25.8047 18.4952 25.8047 19.1537C25.8047 19.8122 26.3385 20.346 26.997 20.346Z"
          stroke="#7200FF"
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M26.997 35.846C27.6555 35.846 28.1893 35.3122 28.1893 34.6537C28.1893 33.9952 27.6555 33.4614 26.997 33.4614C26.3385 33.4614 25.8047 33.9952 25.8047 34.6537C25.8047 35.3122 26.3385 35.846 26.997 35.846Z"
          stroke="#7200FF"
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <rect x="30" y="30" width="24" height="24" rx="12" fill="#7200FF" />
        <path d="M37 42H47H37Z" fill="#7200FF" />
        <path
          d="M37 42H47"
          stroke="white"
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>

      <h3 className="text-center text-[32px] font-bold">
        This feature is not available in your plan
      </h3>
      <div className="text-center text-xl font-medium">
        <p>Your current subscription doesn't include access to this feature.</p>
        <p>
          Please contact your administrator to upgrade your plan or request
          access.
        </p>
      </div>
    </div>
  );
};
