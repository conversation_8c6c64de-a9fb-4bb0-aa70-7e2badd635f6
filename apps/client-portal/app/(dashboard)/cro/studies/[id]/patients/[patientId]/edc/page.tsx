import { Card } from "flowbite-react";

import { ComingSoon } from "@/components/shared/coming-soon";
import { EntitlementGate } from "@/components/shared/entitlement/entitlement-gate";

export default function EdcPage() {
  return (
    <EntitlementGate
      entitlement="EDC_CORE"
      fallback={
        <div className="flex h-full w-full items-center justify-center">
          <Card className="rounded-xl p-8">
            <div className="text-center">
              <h2 className="mb-4 text-2xl font-bold text-gray-900">
                Access Restricted
              </h2>
              <p className="text-gray-600">
                You don't have access to Electronic Data Capture (EDC)
                functionality. Please contact your administrator to request
                access.
              </p>
            </div>
          </Card>
        </div>
      }
    >
      <div className="flex h-full w-full items-center justify-center">
        <ComingSoon />
      </div>
    </EntitlementGate>
  );
}
